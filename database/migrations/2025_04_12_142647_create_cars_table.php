<?php

use App\Enums\Car\BodyType;
use App\Enums\Car\DrivetrainSystem;
use App\Enums\Car\FuelType;
use App\Enums\Car\RegionalSpecification;
use App\Enums\Car\Status;
use App\Enums\Car\TransmissionType;
use App\Enums\City;
use App\Enums\Currency;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cars', function (Blueprint $table) {
            $table->id();
            $table->foreignId('store_id')->constrained();
            $table->foreignId('car_manufacturer_id')->constrained();
            $table->foreignId('car_model_id')->constrained();
            $table->unsignedTinyInteger('cylinder_count');
            $table->enum('regional_specifications', RegionalSpecification::values());
            $table->unsignedSmallInteger('year');
            $table->unsignedInteger('travel_distance_in_km');
            $table->enum('body_type', BodyType::values());
            $table->enum('city', City::values());
            $table->enum('fuel_type', FuelType::values());
            $table->enum('transmission', TransmissionType::values());
            $table->enum('drivetrain_system', DrivetrainSystem::values());
            $table->unsignedTinyInteger('seats_count');
            $table->string('exterior_color');
            $table->string('interior_color');
            $table->unsignedBigInteger('price');
            $table->enum('currency', Currency::values());
            $table->enum('status', Status::values());
            $table->unsignedSmallInteger('engine_capacity');
            $table->unsignedSmallInteger('horse_power')->nullable();
            $table->boolean('has_warranty')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cars');
    }
};
