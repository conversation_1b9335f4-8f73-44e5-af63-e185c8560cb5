<?php

use App\Enums\Car\ImageLabel;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('car_image', function (Blueprint $table) {
            $table->id();
            $table->foreignId('car_id')->constrained();
            $table->unsignedInteger('order')->default(0);
            $table->enum('label', ImageLabel::values());
            $table->foreignId('media_id')->nullable()->constrained();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('car_image');
    }
};
