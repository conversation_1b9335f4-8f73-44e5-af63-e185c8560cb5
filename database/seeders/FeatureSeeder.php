<?php

namespace Database\Seeders;

use App\Enums\Car\FeatureCategory;
use App\Models\Feature;
use Illuminate\Database\Seeder;
use League\Csv\Reader;

class FeatureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $path = storage_path('app/data/features.csv');

        if (! file_exists($path)) {
            $this->command->error("CSV file not found at: {$path}");

            return;
        }

        $csv = Reader::createFromPath($path);
        $csv->setHeaderOffset(0);

        /** @var array{category: string, name_en: string, name_ar: string, description_en: ?string} $record */
        foreach ($csv->getRecords() as $record) {
            Feature::updateOrCreate(
                [
                    'name_en' => $record['name_en'],
                ],
                [
                    'name_ar' => $record['name_ar'],
                    'category' => FeatureCategory::tryFrom($record['category']),
                    'description_en' => $record['description_en'] ?: null,
                ]
            );
        }

        $this->command->info('Features imported successfully!');
    }
}
