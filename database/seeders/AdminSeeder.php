<?php

namespace Database\Seeders;

use App\Enums\UserRole;
use App\Models\User;
use Illuminate\Database\Seeder;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::firstOrCreate([
            'email' => config('settings.default_admin_email'),
        ], [
            'name' => 'Administrator',
            'role' => UserRole::ADMIN,
            'password' => config('settings.default_admin_password'),
        ]);
    }
}
