<?php

namespace Database\Seeders;

use App\Models\CarManufacturer;
use App\Models\CarModel;
use Illuminate\Database\Seeder;
use League\Csv\Exception;
use League\Csv\Reader;
use League\Csv\UnavailableStream;

class CarManufacturerAndModelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @throws UnavailableStream
     * @throws Exception
     */
    public function run(): void
    {
        $path = storage_path('app/data/car_manufacturers.csv');

        if (! file_exists($path)) {
            $this->command->error("CSV file not found at: {$path}");

            return;
        }

        $csv = Reader::createFromPath($path);
        $csv->setHeaderOffset(0);

        /** @var array{name_ar: string, name_en:string, model_ar: string, model_en: string} $record */
        foreach ($csv->getRecords() as $record) {
            $manufacturer = CarManufacturer::updateOrCreate(
                ['name_en' => $record['name_en']],
                ['name_ar' => $record['name_ar']]
            );

            CarModel::updateOrCreate(
                [
                    'name_en' => $record['model_en'],
                    'car_manufacturer_id' => $manufacturer->id,
                ],
                [
                    'name_ar' => $record['model_ar'],
                ]
            );
        }

        $this->command->info('Car manufacturers and models imported successfully!');
    }
}
