<?php

namespace Database\Seeders;

use App\Enums\StoreType;
use App\Models\Store;
use App\Models\User;
use Illuminate\Database\Seeder;

class StoreSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $owner = User::firstOrCreate([
            'email' => '<EMAIL>',
        ], [
            'name' => 'Al Maktb Owner',
            'password' => 'password',
        ]);

        Store::firstOrCreate([
            'name_ar' => 'المكتب',
            'name_en' => 'Al Maktb',
            'type' => StoreType::BUSINESS,
            'owner_id' => $owner->getKey(),
        ]);
    }
}
