<?php

namespace App\Filament\Shared\Components;

use App\Models\CarModel;
use Filament\Forms\Components\Select;
use Illuminate\Database\Eloquent\Builder;

class CarModelSelect extends Select
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->label(__('resources.car.fields.car_model'));
        $this->relationship('carModel', 'name_en', fn (Builder $query) => $query->select('id', 'name_ar', 'name_en'));
        $this->getOptionLabelFromRecordUsing(fn (CarModel $record) => $record->name);
    }
}
