<?php

namespace App\Filament\Shared\Components;

use App\Models\CarManufacturer;
use Filament\Forms\Components\Select;
use Illuminate\Database\Eloquent\Builder;

class CarManufacturerSelect extends Select
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->label(__('resources.car.fields.car_manufacturer'));
        $this->relationship('carManufacturer', 'name_en', fn (Builder $query) => $query->select('id', 'name_ar', 'name_en'));
        $this->getOptionLabelFromRecordUsing(fn (CarManufacturer $record) => $record->name);
    }
}
