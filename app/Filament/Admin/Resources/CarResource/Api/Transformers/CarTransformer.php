<?php

namespace App\Filament\Admin\Resources\CarResource\Api\Transformers;

use App\Models\Car;
use App\Models\CarImage;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Car
 *
 * @property Car $resource
 */
class CarTransformer extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<mixed>
     */
    public function toArray(Request $request): array
    {
        return array_merge($this->resource->toArray(), [
            'images' => $this->whenLoaded('images', fn () => $this->resource->images->map(fn (CarImage $carImage) => [
                'label' => $carImage->label,
                'url' => $carImage->getFirstMediaUrl(Car::DEFAULT_IMAGES_COLLECTION_NAME),
            ])),
            'preview_images' => $this->whenLoaded('previewImages', fn () => $this->resource->previewImages->map(fn (CarImage $carImage) => [
                'label' => $carImage->label,
                'url' => $carImage->getFirstMediaUrl(Car::DEFAULT_IMAGES_COLLECTION_NAME),
            ])),
        ]);
    }
}
