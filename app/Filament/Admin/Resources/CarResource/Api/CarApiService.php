<?php

namespace App\Filament\Admin\Resources\CarResource\Api;

use App\Filament\Admin\Resources\CarResource;
use Rupadana\ApiService\ApiService;

class CarApiService extends ApiService
{
    protected static ?string $resource = CarResource::class;

    public static function handlers(): array
    {
        return [
            Handlers\PaginationHandler::class,
            Handlers\DetailHandler::class,
        ];
    }
}
