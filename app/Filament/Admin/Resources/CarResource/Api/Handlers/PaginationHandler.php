<?php

namespace App\Filament\Admin\Resources\CarResource\Api\Handlers;

use App\Filament\Admin\Resources\CarResource;
use App\Filament\Admin\Resources\CarResource\Api\Transformers\CarTransformer;
use App\Models\Car;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Rupadana\ApiService\Http\Handlers;
use Spatie\QueryBuilder\QueryBuilder;

class PaginationHandler extends Handlers
{
    public static ?string $uri = '/';
    public static ?string $resource = CarResource::class;
    public static bool $public = true;

    /**
     * List of Car
     */
    public function handler(Request $request): AnonymousResourceCollection
    {
        /** @var Builder<Car> $query */
        $query = static::getEloquentQuery();

        $query = QueryBuilder::for($query)
            ->allowedFields($this->getAllowedFields())
            ->allowedSorts($this->getAllowedSorts())
            ->allowedFilters($this->getAllowedFilters())
            ->allowedIncludes($this->getAllowedIncludes())
            ->with([
                'store:id,name_ar,name_en',
                'carManufacturer:id,name_ar,name_en',
                'carModel:id,name_ar,name_en',
                'previewImages.media',
            ])
            ->withCount('images')
            ->paginate((int) $request->query('per_page'))
            ->appends($request->query());

        return CarTransformer::collection($query);
    }
}
