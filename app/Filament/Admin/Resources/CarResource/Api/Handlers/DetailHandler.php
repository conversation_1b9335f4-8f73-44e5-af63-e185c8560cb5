<?php

namespace App\Filament\Admin\Resources\CarResource\Api\Handlers;

use App\Filament\Admin\Resources\CarResource;
use App\Filament\Admin\Resources\CarResource\Api\Transformers\CarTransformer;
use App\Models\Car;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Rupadana\ApiService\Http\Handlers;
use Spatie\QueryBuilder\QueryBuilder;

class DetailHandler extends Handlers
{
    public static ?string $uri = '/{id}';
    public static ?string $resource = CarResource::class;

    protected static bool $public = true;

    /**
     * Show Car
     */
    public function handler(Request $request): CarTransformer|JsonResponse
    {
        $id = $request->route('id');

        /** @var Builder<Car> $query */
        $query = static::getEloquentQuery();

        /** @var string $key */
        $key = static::getKeyName();

        $query = QueryBuilder::for(
            $query->where($key, $id)
        )
            ->with([
                'store:id,name_ar,name_en',
                'carManufacturer:id,name_ar,name_en',
                'carModel:id,name_ar,name_en',
                'images.media',
                'features:id,name_ar,name_en,category',
            ])
            ->withCount('images')
            ->first();

        if (! $query) {
            return static::sendNotFoundResponse();
        }

        return CarTransformer::make($query);
    }
}
