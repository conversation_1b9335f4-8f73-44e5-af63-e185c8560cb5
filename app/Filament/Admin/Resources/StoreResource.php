<?php

namespace App\Filament\Admin\Resources;

use App\Enums\StoreType;
use App\Filament\Admin\Resources\StoreResource\Pages;
use App\Models\Store;
use Dotswan\MapPicker\Fields\Map;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class StoreResource extends Resource
{
    protected static ?string $model = Store::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getModelLabel(): string
    {
        return trans_choice('resources.store.label', 1);
    }

    public static function getPluralModelLabel(): string
    {
        return trans_choice('resources.store.label', 2);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Fieldset::make('owner')
                    ->relationship('owner')
                    ->label(__('resources.store.fields.owner'))
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label(__('resources.user.fields.name'))
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->label(__('resources.user.fields.email'))
                            ->required()
                            ->email()
                            ->unique()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('password')
                            ->hiddenOn('edit')
                            ->label(__('resources.user.fields.password'))
                            ->required()
                            ->password(),
                    ]),
                Forms\Components\TextInput::make('name_ar')
                    ->label(__('resources.store.fields.name_ar'))
                    ->required(),
                Forms\Components\TextInput::make('name_en')
                    ->label(__('resources.store.fields.name_en'))
                    ->required(),
                Forms\Components\Textarea::make('address_ar')
                    ->label(__('resources.store.fields.address_ar')),
                Forms\Components\Textarea::make('address_en')
                    ->label(__('resources.store.fields.address_en')),
                Forms\Components\Select::make('type')
                    ->label(__('resources.store.fields.type'))
                    ->enum(StoreType::class)
                    ->options(StoreType::class)
                    ->required(),
                Forms\Components\Fieldset::make()
                    ->label(__('resources.store.fields.location'))
                    ->schema([
                        Forms\Components\TextInput::make('latitude')
                            ->label(__('resources.store.fields.latitude'))
                            ->numeric(),
                        Forms\Components\TextInput::make('longitude')
                            ->label(__('resources.store.fields.longitude'))
                            ->numeric(),
                        Map::make('location')
                            ->label(__('resources.store.fields.location'))
                            ->dehydrated(false)
                            ->columnSpanFull()
                            // Basic Configuration
                            ->defaultLocation(latitude: 33.510394, longitude: 36.2879831)
                            ->draggable()
                            ->clickable(true)
                            ->zoom(15)
                            ->minZoom(0)
                            ->maxZoom(28)
                            ->tilesUrl('https://tile.openstreetmap.de/{z}/{x}/{y}.png')
                            ->detectRetina()
                            /** @param ?array{lat: float, lng: float, geojson: string} $state */
                            ->afterStateUpdated(function (Forms\Set $set, ?array $state): void {
                                if (! $state) {
                                    return;
                                }
                                $set('latitude', $state['lat']);
                                $set('longitude', $state['lng']);
                            })
                            ->afterStateHydrated(function ($state, ?Store $record, Forms\Set $set): void {
                                if (is_null($record)) {
                                    return;
                                }

                                $set('location', [
                                    'lat' => $record->latitude,
                                    'lng' => $record->longitude,
                                ]);
                            }),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('owner.name')
                    ->label(__('resources.store.fields.owner_name'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name_ar')
                    ->label(__('resources.store.fields.name_ar'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('name_en')
                    ->label(__('resources.store.fields.name_en'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('address_ar')
                    ->label(__('resources.store.fields.address_ar'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('address_en')
                    ->label(__('resources.store.fields.address_en'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('latitude')
                    ->label(__('resources.store.fields.latitude'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('longitude')
                    ->label(__('resources.store.fields.longitude'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->label(__('resources.store.fields.type'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStores::route('/'),
            'create' => Pages\CreateStore::route('/create'),
            'edit' => Pages\EditStore::route('/{record}/edit'),
        ];
    }
}
