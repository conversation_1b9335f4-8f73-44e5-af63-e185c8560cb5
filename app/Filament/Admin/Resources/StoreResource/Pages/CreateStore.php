<?php

namespace App\Filament\Admin\Resources\StoreResource\Pages;

use App\Enums\UserRole;
use App\Filament\Admin\Resources\StoreResource;
use App\Models\Store;
use Filament\Resources\Pages\CreateRecord;

/**
 * @property Store $record
 */
class CreateStore extends CreateRecord
{
    protected static string $resource = StoreResource::class;

    protected function afterCreate(): void
    {
        if (! $this->record->owner) {
            return;
        }

        $this->record->owner->role = UserRole::STORE_OWNER;
        $this->record->owner->save();
    }
}
