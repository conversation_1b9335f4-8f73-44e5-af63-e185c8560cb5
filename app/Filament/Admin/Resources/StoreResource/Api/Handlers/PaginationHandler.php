<?php

namespace App\Filament\Admin\Resources\StoreResource\Api\Handlers;

use App\Filament\Admin\Resources\StoreResource;
use App\Filament\Admin\Resources\StoreResource\Api\Transformers\StoreTransformer;
use App\Models\Store;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Rupadana\ApiService\Http\Handlers;
use Spatie\QueryBuilder\QueryBuilder;

class PaginationHandler extends Handlers
{
    public static ?string $uri = '/';
    public static ?string $resource = StoreResource::class;
    public static bool $public = true;

    /**
     * List of Store
     */
    public function handler(Request $request): AnonymousResourceCollection
    {
        /** @var Builder<Store> $query */
        $query = static::getEloquentQuery();

        $query = QueryBuilder::for($query)
            ->allowedFields($this->getAllowedFields())
            ->allowedSorts($this->getAllowedSorts())
            ->allowedFilters($this->getAllowedFilters())
            ->allowedIncludes($this->getAllowedIncludes())
            ->paginate((int) $request->query('per_page'))
            ->appends($request->query());

        return StoreTransformer::collection($query);
    }
}
