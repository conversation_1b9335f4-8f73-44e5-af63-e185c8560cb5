<?php

namespace App\Filament\Admin\Resources\StoreResource\Api;

use App\Filament\Admin\Resources\StoreResource;
use Rupadana\ApiService\ApiService;

class StoreApiService extends ApiService
{
    protected static ?string $resource = StoreResource::class;

    /**
     * @return class-string[]
     */
    public static function handlers(): array
    {
        return [
            Handlers\PaginationHandler::class,
            Handlers\DetailHandler::class,
        ];

    }
}
