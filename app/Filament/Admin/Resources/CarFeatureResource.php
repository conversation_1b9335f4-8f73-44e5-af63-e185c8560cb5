<?php

namespace App\Filament\Admin\Resources;

use App\Enums\Car\FeatureCategory;
use App\Filament\Admin\Resources\CarFeatureResource\Pages;
use App\Models\Feature;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CarFeatureResource extends Resource
{
    protected static ?string $model = Feature::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getModelLabel(): string
    {
        return trans_choice('resources.car_feature.label', 1);
    }

    public static function getPluralModelLabel(): string
    {
        return trans_choice('resources.car_feature.label', 2);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name_ar')
                    ->label(__('resources.car_feature.fields.name_ar'))
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('name_en')
                    ->label(__('resources.car_feature.fields.name_en'))
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('category')
                    ->label(__('resources.car_feature.fields.category'))
                    ->enum(FeatureCategory::class)
                    ->options(FeatureCategory::class)
                    ->required(),
                Forms\Components\Textarea::make('description_ar')
                    ->label(__('resources.car_feature.fields.description_ar'))
                    ->columnStart(1),
                Forms\Components\Textarea::make('description_en')
                    ->label(__('resources.car_feature.fields.description_en')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label(__('resources.car_feature.fields.name_ar'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('name_en')
                    ->label(__('resources.car_feature.fields.name_en'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('category')
                    ->label(__('resources.car_feature.fields.category'))
                    ->badge()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCarFeatures::route('/'),
            'create' => Pages\CreateCarFeature::route('/create'),
            'edit' => Pages\EditCarFeature::route('/{record}/edit'),
        ];
    }
}
