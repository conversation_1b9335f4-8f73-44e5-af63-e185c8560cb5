<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\CarManufacturerResource\RelationManagers\CarModelsRelationManager;
use App\Filament\Admin\Resources\CarModelResource\Pages;
use App\Filament\Shared\Components\CarManufacturerSelect;
use App\Models\CarModel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CarModelResource extends Resource
{
    protected static ?string $model = CarModel::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getModelLabel(): string
    {
        return trans_choice('resources.car_model.label', 1);
    }

    public static function getPluralModelLabel(): string
    {
        return trans_choice('resources.car_model.label', 2);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name_ar')
                    ->label(__('resources.car_model.fields.name_ar'))
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('name_en')
                    ->label(__('resources.car_model.fields.name_en'))
                    ->required()
                    ->maxLength(255),
                CarManufacturerSelect::make('car_manufacturer_id')
                    ->hiddenOn(CarModelsRelationManager::class)
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label(__('resources.car_model.fields.name_ar'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('name_en')
                    ->label(__('resources.car_model.fields.name_en'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('carManufacturer.name')
                    ->label(__('resources.car_model.fields.car_manufacturer'))
                    ->hiddenOn(CarModelsRelationManager::class)
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCarModels::route('/'),
            'create' => Pages\CreateCarModel::route('/create'),
            'edit' => Pages\EditCarModel::route('/{record}/edit'),
        ];
    }
}
