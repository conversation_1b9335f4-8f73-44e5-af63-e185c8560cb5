<?php

namespace App\Filament\Admin\Resources\CarManufacturerResource\RelationManagers;

use App\Filament\Admin\Resources\CarModelResource;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class CarModelsRelationManager extends RelationManager
{
    protected static string $relationship = 'carModels';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return self::getPluralModelLabel();
    }

    public static function getPluralModelLabel(): string
    {
        return trans_choice('resources.car_model.label', 2);
    }

    protected static function getModelLabel(): string
    {
        return trans_choice('resources.car_model.label', 1);
    }

    public function form(Form $form): Form
    {
        return CarModelResource::form($form);
    }

    public function table(Table $table): Table
    {
        return CarModelResource::table($table)
            ->recordTitleAttribute('name')
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
