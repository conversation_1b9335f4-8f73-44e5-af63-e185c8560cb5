<?php

namespace App\Enums;

use App\Traits\EnumToArray;
use Filament\Support\Contracts\HasLabel;

enum City: string implements HasLabel
{
    use EnumToArray;

    case DAMASCUS = 'damascus';
    case RIF_DIMASHQ = 'rif-dimashq';
    case ALEPPO = 'aleppo';
    case HOMS = 'homs';
    case HAMA = 'hama';
    case LATAKIA = 'latakia';
    case TARTUS = 'tartus';
    case IDLIB = 'idlib';
    case DEIR_EZ_ZOR = 'deir-ez-zor';
    case RAQQA = 'raqqa';
    case HASAKAH = 'hasakah';
    case SUWAYDA = 'suwayda';
    case DARAA = 'daraa';
    case QUNEITRA = 'quneitra';

    public function getLabel(): string
    {
        return __("enums.city.{$this->value}");
    }
}
