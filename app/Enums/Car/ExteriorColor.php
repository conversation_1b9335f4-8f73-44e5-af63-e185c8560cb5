<?php

namespace App\Enums\Car;

use Filament\Support\Contracts\HasLabel;

enum ExteriorColor: string implements HasLabel
{
    case WHITE = 'white';
    case BLACK = 'black';
    case GRAY = 'gray';
    case SILVER = 'silver';
    case RED = 'red';
    case BLUE = 'blue';
    case YELLOW = 'yellow';
    case GREEN = 'green';
    case BEIGE = 'beige';
    case BROWN = 'brown';
    case ORANGE = 'orange';
    case GOLD = 'gold';

    public function getLabel(): string
    {
        return __('enums.exterior_color.' . $this->value);
    }
}
