<?php

namespace App\Enums\Car;

use App\Traits\EnumToArray;
use Filament\Support\Contracts\HasLabel;

enum BodyType: string implements HasLabel
{
    use EnumToArray;

    case SUV = 'suv';
    case COUPE = 'coupe';
    case SEDAN = 'sedan';
    case CROSSOVER = 'crossover';
    case HARD_TOP_CONVERTABLE = 'hard_top_convertable';
    case SOFT_TOP_CONVERTABLE = 'soft_top_convertable';
    case PICK_UP_TRUCK = 'pick_up_truck';
    case HATCHBACK = 'hatchback';
    case SPORTS_CAR = 'sports_car';
    case VAN = 'van';
    case MINI_VAN = 'mini_van';
    case STATION_WAGON = 'wagon';
    case OTHER = 'other';

    public function getLabel(): string
    {
        return __("enums.body_type.{$this->value}");
    }

    /**
     * @return array<int> Array of possible seat counts
     */
    public function getSeatsCounts(): array
    {
        return match ($this) {
            self::COUPE, self::SPORTS_CAR, self::HARD_TOP_CONVERTABLE, self::SOFT_TOP_CONVERTABLE => [2, 4, 5],
            self::SEDAN, self::HATCHBACK => [4, 5],
            self::SUV => [4, 5, 6, 7, 8],
            self::CROSSOVER, self::STATION_WAGON => [4, 5, 6, 7],
            self::MINI_VAN, self::VAN => range(2, 15),
            self::PICK_UP_TRUCK => [2, 3, 4, 5],
            self::OTHER => range(2, 20),
        };
    }

    public function acceptsOther(): bool
    {
        return in_array($this, [self::SUV, self::CROSSOVER, self::STATION_WAGON, self::MINI_VAN, self::VAN, self::OTHER]);
    }
}
