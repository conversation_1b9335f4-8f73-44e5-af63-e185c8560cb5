<?php

namespace App\Enums\Car;

use App\Traits\EnumToArray;
use Filament\Support\Contracts\HasLabel;

enum FeatureCategory: string implements HasLabel
{
    use EnumToArray;

    case SAFETY_AND_SECURITY = 'safety_and_security';
    case COMFORT = 'comfort';
    case EXTERIOR = 'exterior';
    case ENTERTAINMENT = 'entertainment';
    case OTHER = 'other';

    public function getLabel(): string
    {
        return __("enums.feature_category.{$this->value}");
    }
}
