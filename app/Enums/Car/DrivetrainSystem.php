<?php

namespace App\Enums\Car;

use App\Traits\EnumToArray;
use Filament\Support\Contracts\HasLabel;

enum DrivetrainSystem: string implements HasLabel
{
    use EnumToArray;

    case FRONT_WHEEL_DRIVE = 'fwd';
    case REAR_WHEEL_DRIVE = 'rwd';
    case FOUR_WHEEL_DRIVE = '4wd';
    case ALL_WHEEL_DRIVE = 'awd';

    public function getLabel(): string
    {
        return __("enums.drivetrain_system.{$this->value}");
    }
}
