<?php

namespace App\Enums\Car;

use App\Traits\EnumToArray;
use Filament\Support\Contracts\HasLabel;

enum ImageLabel: string implements HasLabel
{
    use EnumToArray;

    case FRONT_ANGLE = 'front_angle';
    case REAR_ANGLE = 'rear_angle';
    case ENGINE = 'engine';
    case EXTERIOR_ROOF = 'exterior_roof';
    case INTERIOR_FRONT = 'interior_front';
    case ODOMETER = 'odometer';
    case TRANSMISSION = 'transmission';
    case TIRE = 'tire';
    case STEREO = 'stereo';
    case REAR_SEATS = 'rear_seats';
    case TRUNK_VIEW = 'trunk_view';
    case FRONT_MIRROR = 'front_mirror';
    case STEERING = 'steering';
    case DASHBOARD = 'dashboard';
    case FEATURES = 'features';
    case INTERIOR_ROOF = 'interior_roof';
    case FRONT_AC = 'front_ac';
    case REAR_AC = 'rear_ac';
    case KEYS = 'keys';
    case SIDE_VIEW = 'side_view';
    case REAR_VIEW = 'rear_view';
    case DOORS = 'doors';
    case FINGERPRINT = 'fingerprint';
    case EXHAUST = 'exhaust';
    case TRUNK_FUEL_BUTTONS = 'trunk_fuel_buttons';

    public function getLabel(): string
    {
        return __('enums.image_label.' . $this->value);
    }

    public function getExampleImageSrc(): string
    {
        return 'https://placehold.co/1200x600?text=' . str($this->value)->replace('_', ' ')->title();
    }
}
