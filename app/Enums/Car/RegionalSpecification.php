<?php

namespace App\Enums\Car;

use App\Traits\EnumToArray;
use Filament\Support\Contracts\HasLabel;

enum RegionalSpecification: string implements HasLabel
{
    use EnumToArray;

    case GCC = 'gcc';
    case AMERICAN = 'american';
    case CANADIAN = 'canadian';
    case EUROPEAN = 'european';
    case JAPANESE = 'japanese';
    case KOREAN = 'korean';
    case CHINESE = 'chinese';
    case OTHER = 'other';

    public function getLabel(): string
    {
        return __("enums.regional_specification.{$this->value}");
    }
}
