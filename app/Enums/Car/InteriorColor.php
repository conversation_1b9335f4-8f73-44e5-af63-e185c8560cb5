<?php

namespace App\Enums\Car;

use Filament\Support\Contracts\HasLabel;

enum InteriorColor: string implements HasLabel
{
    case BLACK = 'black';
    case GRAY = 'gray';
    case BEIGE = 'beige';
    case WHITE = 'white';
    case BROWN = 'brown';
    case TAN = 'tan';
    case IVORY = 'ivory';
    case BLUE = 'blue';
    case RED = 'red';
    case TWO_TONE = 'two_tone';
    case CUSTOM_DESIGN = 'custom_design';

    public function getLabel(): string
    {
        return __('enums.interior_color.' . $this->value);
    }
}
