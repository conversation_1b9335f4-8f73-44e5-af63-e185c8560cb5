<?php

namespace App\Enums\Car;

use App\Traits\EnumToArray;
use Filament\Support\Contracts\HasLabel;

enum TransmissionType: string implements HasLabel
{
    use EnumToArray;

    case AUTOMATIC = 'automatic';
    case MANUAL = 'manual';
    case TIPTRONIC = 'tiptronic';
    case CVT = 'cvt';
    case DCT = 'dct';

    public function getLabel(): string
    {
        return __("enums.transmission_type.{$this->value}");
    }
}
