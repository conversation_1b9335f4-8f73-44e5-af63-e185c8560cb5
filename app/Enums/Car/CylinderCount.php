<?php

namespace App\Enums\Car;

use App\Traits\EnumToArray;
use Filament\Support\Contracts\HasLabel;

enum CylinderCount: int implements HasLabel
{
    use EnumToArray;

    case ONE = 1;
    case TWO = 2;
    case THREE = 3;
    case FOUR = 4;
    case FIVE = 5;
    case SIX = 6;
    case EIGHT = 8;
    case TEN = 10;
    case TWELVE = 12;
    case SIXTEEN = 16;

    public function getLabel(): string
    {
        return (string) $this->value;
    }
}
