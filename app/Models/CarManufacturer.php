<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $name_ar
 * @property string $name_en
 */
class CarManufacturer extends Model
{
    /**
     * @return HasMany<CarModel, $this>
     */
    public function carModels(): HasMany
    {
        return $this->hasMany(CarModel::class);
    }

    /**
     * @return Attribute<string, never>
     */
    protected function name(): Attribute
    {
        return Attribute::get(
            /** @param array{name_ar: string, name_en: string} $attributes */
            function ($value, array $attributes): string {
                /** @var string $arabicName */
                $arabicName = $attributes['name_ar'];

                /** @var string $englishName */
                $englishName = $attributes['name_en'];

                if (app()->getLocale() === 'en' || $arabicName === $englishName) {
                    return $englishName;
                }

                return $arabicName . ' | ' . $englishName;
            },
        );
    }
}
