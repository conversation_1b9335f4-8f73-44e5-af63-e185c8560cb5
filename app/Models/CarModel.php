<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CarModel extends Model
{
    /**
     * @return BelongsTo<CarManufacturer, $this>
     */
    public function carManufacturer(): BelongsTo
    {
        return $this->belongsTo(CarManufacturer::class);
    }

    /**
     * @return Attribute<string, never>
     */
    protected function name(): Attribute
    {
        return Attribute::get(
            /** @param array{name_ar: string, name_en: string} $attributes */
            function ($value, array $attributes): string {
                /** @var string $arabicName */
                $arabicName = $attributes['name_ar'];

                /** @var string $englishName */
                $englishName = $attributes['name_en'];

                if (app()->getLocale() === 'en' || $arabicName === $englishName) {
                    return $englishName;
                }

                return $arabicName . ' | ' . $englishName;
            },
        );
    }
}
