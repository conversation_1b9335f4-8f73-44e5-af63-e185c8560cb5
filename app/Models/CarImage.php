<?php

namespace App\Models;

use App\Enums\Car\ImageLabel;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * @property ImageLabel $label
 */
class CarImage extends Pivot implements HasMedia
{
    use InteractsWithMedia;

    public $incrementing = true;

    protected function casts(): array
    {
        return [
            'label' => ImageLabel::class,
        ];
    }
}
