variables:
  DEPLOY_PATH: "/hosting/www/$CI_PROJECT_NAME"

deploy-live:
  only:
    - main
  stage: deploy
  script:
    - cd $DEPLOY_PATH
    - docker exec $CI_PROJECT_NAME git reset --hard HEAD
    - docker exec $CI_PROJECT_NAME git pull origin main
    - docker exec $CI_PROJECT_NAME composer install --no-dev --optimize-autoloader
    - docker exec $CI_PROJECT_NAME php artisan migrate --force
    - docker exec $CI_PROJECT_NAME php artisan optimize
    - docker exec $CI_PROJECT_NAME chown -R www-data:995 .
